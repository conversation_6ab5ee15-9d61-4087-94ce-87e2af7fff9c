import React, { useState, useEffect } from 'react';

import { FormField } from '@/components/custom/arito/form/form-field';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { initialFormValues, searchSchema } from '../schema';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { Label } from '@/components/ui/label';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [formData, setFormData] = useState(initialFormValues);

  const handleSubmit = (data: any) => {
    onSearch(data);
    setIsFormDirty(false);
    onClose();
  };

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
    }
  }, [open]);

  return (
    <AritoDialog
      open={open}
      onClose={handleClose}
      title='Khai báo hệ số phân bổ khấu hao TSCĐ'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialFormValues}
        onSubmit={handleSubmit}
        className='w-[800px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <div className='space-y-2 p-4'>
              <div className='flex flex-col space-y-1'>
                <div className='flex items-center'>
                  <Label className='w-40 min-w-40'>Ngày báo cáo:</Label>
                  <div>
                    <FormField name='reportDate' type='date' label='' className='w-[206px]' />
                  </div>
                </div>
              </div>
            </div>
          </div>
        }
        bottomBar={<BottomBar mode={formMode} onSubmit={() => handleSubmit(formData)} onClose={onClose} />}
        classNameBottomBar='relative'
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
