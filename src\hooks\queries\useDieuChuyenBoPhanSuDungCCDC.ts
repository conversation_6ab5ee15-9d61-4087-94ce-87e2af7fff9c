import { useState, useEffect } from 'react';
import {
  DieuChuyenBoPhanSuDungCCDC,
  DieuChuyenBoPhanSuDungCCDCInput,
  DieuChuyenBoPhanSuDungCCDCResponse
} from '@/types/schemas/dieu-chuyen-bo-phan-su-dung-ccdc.type';
import { useAuth } from '@/contexts/auth-context';
import QUERY_KEYS from '@/constants/query-keys';
import api from '@/lib/api';

interface UseDieuChuyenBoPhanSuDungCCDCReturn {
  dieuChuyenBoPhanSuDungCCDCs: DieuChuyenBoPhanSuDungCCDC[];
  isLoading: boolean;
  addDieuChuyenBoPhanSuDungCCDC: (newTransfer: DieuChuyenBoPhanSuDungCCDCInput) => Promise<DieuChuyenBoPhanSuDungCCDC>;
  updateDieuChuyenBoPhanSuDungCCDC: (uuid: string, updatedTransfer: DieuChuyenBoPhanSuDungCCDCInput) => Promise<DieuChuyenBoPhanSuDungCCDC>;
  deleteDieuChuyenBoPhanSuDungCCDC: (uuid: string) => Promise<void>;
  refreshDieuChuyenBoPhanSuDungCCDCs: () => Promise<void>;
  getDieuChuyenBoPhanSuDungCCDCByCode: (code: string) => Promise<DieuChuyenBoPhanSuDungCCDC | null>;
  getActiveDieuChuyenBoPhanSuDungCCDCs: () => Promise<DieuChuyenBoPhanSuDungCCDC[]>;
}

/**
 * Hook for managing DieuChuyenBoPhanSuDungCCDC (Tool Transfer Department Usage) data
 *
 * This hook provides functions to fetch, create, update, and delete tool transfers.
 */
export const useDieuChuyenBoPhanSuDungCCDC = (initialTransfers: DieuChuyenBoPhanSuDungCCDC[] = []): UseDieuChuyenBoPhanSuDungCCDCReturn => {
  const [dieuChuyenBoPhanSuDungCCDCs, setDieuChuyenBoPhanSuDungCCDCs] = useState<DieuChuyenBoPhanSuDungCCDC[]>(initialTransfers);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchDieuChuyenBoPhanSuDungCCDCs = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<DieuChuyenBoPhanSuDungCCDCResponse>(`/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHUYEN_BO_PHAN_SU_DUNG_CCDC}/`);
      setDieuChuyenBoPhanSuDungCCDCs(response.data.results);
    } catch (error) {
      console.error('Error fetching tool transfers:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getDieuChuyenBoPhanSuDungCCDCByCode = async (code: string): Promise<DieuChuyenBoPhanSuDungCCDC | null> => {
    if (!entity?.slug) return null;

    setIsLoading(true);
    try {
      const response = await api.get<DieuChuyenBoPhanSuDungCCDCResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHUYEN_BO_PHAN_SU_DUNG_CCDC}/?code=${code}`
      );

      if (response.data.results.length > 0) {
        return response.data.results[0];
      }
      return null;
    } catch (error) {
      console.error('Error fetching tool transfer by code:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const getActiveDieuChuyenBoPhanSuDungCCDCs = async (): Promise<DieuChuyenBoPhanSuDungCCDC[]> => {
    if (!entity?.slug) return [];

    setIsLoading(true);
    try {
      const response = await api.get<DieuChuyenBoPhanSuDungCCDCResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHUYEN_BO_PHAN_SU_DUNG_CCDC}/?active=true`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching active tool transfers:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const addDieuChuyenBoPhanSuDungCCDC = async (newTransfer: DieuChuyenBoPhanSuDungCCDCInput): Promise<DieuChuyenBoPhanSuDungCCDC> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      const response = await api.post<DieuChuyenBoPhanSuDungCCDC>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHUYEN_BO_PHAN_SU_DUNG_CCDC}/`,
        newTransfer
      );
      setDieuChuyenBoPhanSuDungCCDCs(prev => [...prev, response.data]);
      return response.data;
    } catch (error) {
      console.error('Error adding tool transfer:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateDieuChuyenBoPhanSuDungCCDC = async (uuid: string, updatedTransfer: DieuChuyenBoPhanSuDungCCDCInput): Promise<DieuChuyenBoPhanSuDungCCDC> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      const response = await api.put<DieuChuyenBoPhanSuDungCCDC>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHUYEN_BO_PHAN_SU_DUNG_CCDC}/${uuid}/`,
        updatedTransfer
      );
      setDieuChuyenBoPhanSuDungCCDCs(prev =>
        prev.map(item => (item.uuid === uuid ? response.data : item))
      );
      return response.data;
    } catch (error) {
      console.error('Error updating tool transfer:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteDieuChuyenBoPhanSuDungCCDC = async (uuid: string): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHUYEN_BO_PHAN_SU_DUNG_CCDC}/${uuid}/`);
      setDieuChuyenBoPhanSuDungCCDCs(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting tool transfer:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDieuChuyenBoPhanSuDungCCDCs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entity?.slug]);

  return {
    dieuChuyenBoPhanSuDungCCDCs,
    isLoading,
    addDieuChuyenBoPhanSuDungCCDC,
    updateDieuChuyenBoPhanSuDungCCDC,
    deleteDieuChuyenBoPhanSuDungCCDC,
    refreshDieuChuyenBoPhanSuDungCCDCs: fetchDieuChuyenBoPhanSuDungCCDCs,
    getDieuChuyenBoPhanSuDungCCDCByCode,
    getActiveDieuChuyenBoPhanSuDungCCDCs
  };
};
