import { GridColDef } from '@mui/x-data-grid';

export function getDataTableColumns(): GridColDef[] {
  return [
    { field: 'ma_ts', headerName: 'Mã tài sản', width: 120 },
    { field: 'ten_ts', headerName: 'Tên tài sản', width: 200 },
    { field: 'he_so', headerName: 'Hệ số', width: 80 },
    { field: 'tk_kh', headerName: 'Tài khoản khấu hao', width: 150, renderCell: (params: any) => params.row.tk_kh_data?.code || '' },
    { field: 'tk_cp', headerName: 'Tài khoản chi phí', width: 150, renderCell: (params: any) => params.row.tk_cp_data?.code || '' },
    { field: 'ma_bp_ts', headerName: 'Bộ phận sử dụng', width: 150, renderCell: (params: any) => params.row.ma_bp_ts_data?.ma_bp || '' },
    { field: 'ma_bp', headerName: 'Bộ phận', width: 120, renderCell: (params: any) => params.row.ma_bp_data?.ma_bp || '' },
    { field: 'ma_vu_viec', headerName: 'Vụ việc', width: 120, renderCell: (params: any) => params.row.ma_vv_data?.ma_vu_viec || '' },
    { field: 'ma_sp', headerName: 'Mã sản phẩm', width: 150, renderCell: (params: any) => params.row.ma_sp_data?.ma_vt || '' },
    { field: 'ma_hd', headerName: 'Hợp đồng', width: 120, renderCell: (params: any) => params.row.ma_hd_data?.ma_hd || '' },
    { field: 'ma_phi', headerName: 'Mã phí', width: 100, renderCell: (params: any) => params.row.ma_phi_data?.ma_phi || '' }
  ];
}
