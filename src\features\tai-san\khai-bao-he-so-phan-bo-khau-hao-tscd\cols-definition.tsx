import { GridColDef } from '@mui/x-data-grid';
import AritoCheckboxCellRenderer from '@/components/custom/arito/cell-renderers/arito-checkbox-cell-renderer';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';

export const Columns: GridColDef[] = [
  {
    field: 'ma_tai_san',
    headerName: 'Mã tài sản',
    width: 120
  },
  {
    field: 'ten_tai_san',
    headerName: 'Tên tài sản',
    width: 200
  },
  {
    field: 'he_so',
    headerName: 'Hệ số',
    width: 100,
    type: 'number'
  },
  {
    field: 'tk_khau_hao',
    headerName: 'TK khấu hao',
    width: 120
  },
  {
    field: 'tk_chi_phi',
    headerName: 'TK chi phí',
    width: 120
  },
  {
    field: 'bo_phan_su_dung',
    headerName: 'Bộ phận sử dụng',
    width: 150
  },
  {
    field: 'bo_phan',
    headerName: 'B<PERSON> phận',
    width: 120
  },
  {
    field: 'vu_viec',
    headerName: 'Vụ việc',
    width: 120
  },
  {
    field: 'ma_san_pham',
    headerName: 'Mã sản phẩm',
    width: 120
  },
  {
    field: 'hop_dong',
    headerName: 'Hợp đồng',
    width: 120
  },
  {
    field: 'ma_phi',
    headerName: 'Mã phí',
    width: 100
  }
];

export const assetsColumns: ExtendedGridColDef[] = [
  {
    field: 'maTaiSan',
    headerName: 'Mã tài sản',
    width: 150
  },
  {
    field: 'tenTaiSan',
    headerName: 'Tên tài sản',
    width: 300
  }
];

export const vatTuSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'ma_vat_tu',
    headerName: 'Mã vật tư',
    width: 120
  },
  {
    field: 'ten_vat_tu',
    headerName: 'Tên vật tư',
    width: 250
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 100
  },
  {
    field: 'nhom_1',
    headerName: 'Nhóm 1',
    width: 120
  },
  {
    field: 'theo_doi_lo',
    headerName: 'Theo dõi lô',
    width: 120,
    renderCell: AritoCheckboxCellRenderer
  },
  {
    field: 'quy_cach',
    headerName: 'Quy cách',
    width: 120,
    renderCell: AritoCheckboxCellRenderer
  },
  {
    field: 'hinh_anh',
    headerName: 'Hình ảnh',
    width: 120
  }
];

export const getAccountColumns: ExtendedGridColDef[] = [
  {
    field: 'accountCode',
    headerName: 'Mã tài khoản',
    width: 150
  },
  {
    field: 'accountName',
    headerName: 'Tên tài khoản',
    width: 250
  },
  {
    field: 'parentAccount',
    headerName: 'Tài khoản mẹ',
    width: 150
  },
  {
    field: 'isPrimary',
    headerName: 'TK sổ cái',
    width: 80,
    renderCell: AritoCheckboxCellRenderer
  },
  {
    field: 'isDetail',
    headerName: 'TK chi tiết',
    width: 80,
    renderCell: AritoCheckboxCellRenderer
  },
  {
    field: 'level',
    headerName: 'Bậc tk',
    width: 100
  }
];
export const BoPhanColDef: ExtendedGridColDef[] = [
  {
    field: 'departmentCode',
    headerName: 'Mã bộ phận',
    width: 150
  },
  {
    field: 'departmentName',
    headerName: 'Tên bộ phận',
    width: 200
  }
];
export const VuViecColDef: ExtendedGridColDef[] = [
  {
    field: 'projectCode',
    headerName: 'Mã vụ việc',
    width: 120
  },
  {
    field: 'projectName',
    headerName: 'Tên vụ việc',
    width: 200
  }
];
export const FeeColDef: ExtendedGridColDef[] = [
  { field: 'feeCode', headerName: 'Mã phí', width: 120 },
  { field: 'feeName', headerName: 'Tên phí', width: 200 }
];
export const ContractColDef: ExtendedGridColDef[] = [
  {
    field: 'projectCode',
    headerName: 'Mã hợp đồng',
    width: 120
  },
  {
    field: 'projectName',
    headerName: 'Tên hợp đồng',
    width: 200
  }
];
