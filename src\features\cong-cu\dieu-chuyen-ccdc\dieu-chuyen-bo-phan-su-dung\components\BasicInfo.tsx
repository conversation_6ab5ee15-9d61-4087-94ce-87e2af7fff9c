import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { SearchField } from '@/components/custom/arito/form/search-field';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants/query-keys';
import type { BoPhan, AccountModel } from '@/types/schemas';

// Search columns for different entities
const congCuSearchColumns = [
  { field: 'ma_ccdc', headerName: 'Mã CCDC', width: 150 },
  { field: 'ten_ccdc', headerName: 'Tên CCDC', width: 200 }
];

const boPhanSearchColumns = [
  { field: 'ma_bp', headerName: 'Mã bộ phận', width: 150 },
  { field: 'ten_bp', headerName: 'Tên bộ phận', width: 200 }
];

const taiKhoanSearchColumns = [
  { field: 'ma_tk', headerName: 'Mã tài khoản', width: 150 },
  { field: 'ten_tk', headerName: 'Tên tài khoản', width: 200 }
];

interface BasicInfoProps {
  congCu: any | null;
  boPhan: BoPhan | null;
  tkCongCu: AccountModel | null;
  tkPhanBo: AccountModel | null;
  tkChiPhi: AccountModel | null;
  setCongCu: (value: any | null) => void;
  setBoPhan: (value: BoPhan | null) => void;
  setTkCongCu: (value: AccountModel | null) => void;
  setTkPhanBo: (value: AccountModel | null) => void;
  setTkChiPhi: (value: AccountModel | null) => void;
}

const BasicInfo: React.FC<BasicInfoProps> = ({
  congCu,
  boPhan,
  tkCongCu,
  tkPhanBo,
  tkChiPhi,
  setCongCu,
  setBoPhan,
  setTkCongCu,
  setTkPhanBo,
  setTkChiPhi
}) => {
  return (
    <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
      <div className='flex flex-col space-y-4 p-5'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Công cụ:</Label>
          <div>
            <SearchField<any>
              type='text'
              displayRelatedField='ten_ccdc'
              columnDisplay='ma_ccdc'
              searchEndpoint={`/${QUERY_KEYS.CONG_CU}`}
              searchColumns={congCuSearchColumns}
              dialogTitle='Danh mục công cụ'
              placeholder='Chọn công cụ'
              selectedValue={congCu}
              onSelectionChange={setCongCu}
            />
          </div>
        </div>

        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Kỳ/Năm:</Label>
            <div className='flex gap-2'>
              <FormField className='mr-2 w-20 min-w-[150px]' type='number' label='' name='ky' placeholder='5' />
              <FormField className='w-20 min-w-[150px]' type='number' label='' name='nam' placeholder='2025' />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Bộ phận:</Label>
          <div>
            <SearchField<BoPhan>
              type='text'
              displayRelatedField='ten_bp'
              columnDisplay='ma_bp'
              searchEndpoint={`/${QUERY_KEYS.BO_PHAN}`}
              searchColumns={boPhanSearchColumns}
              dialogTitle='Danh mục bộ phận'
              placeholder='Chọn bộ phận'
              selectedValue={boPhan}
              onSelectionChange={setBoPhan}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tk công cụ:</Label>
          <div>
            <SearchField<AccountModel>
              type='text'
              displayRelatedField='ten_tk'
              columnDisplay='ma_tk'
              searchEndpoint={`/${QUERY_KEYS.ACCOUNT}`}
              searchColumns={taiKhoanSearchColumns}
              dialogTitle='Danh mục tài khoản'
              placeholder='000'
              selectedValue={tkCongCu}
              onSelectionChange={setTkCongCu}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tk phân bổ:</Label>
          <div>
            <SearchField<AccountModel>
              type='text'
              displayRelatedField='ten_tk'
              columnDisplay='ma_tk'
              searchEndpoint={`/${QUERY_KEYS.ACCOUNT}`}
              searchColumns={taiKhoanSearchColumns}
              dialogTitle='Danh mục tài khoản'
              placeholder='000'
              selectedValue={tkPhanBo}
              onSelectionChange={setTkPhanBo}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tk chi phí:</Label>
          <div>
            <SearchField<AccountModel>
              type='text'
              displayRelatedField='ten_tk'
              columnDisplay='ma_tk'
              searchEndpoint={`/${QUERY_KEYS.ACCOUNT}`}
              searchColumns={taiKhoanSearchColumns}
              dialogTitle='Danh mục tài khoản'
              placeholder='000'
              selectedValue={tkChiPhi}
              onSelectionChange={setTkChiPhi}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
