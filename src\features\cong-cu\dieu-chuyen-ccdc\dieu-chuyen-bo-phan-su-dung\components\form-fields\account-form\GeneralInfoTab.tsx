import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface GeneralInfoTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const GeneralInfoTab: React.FC<GeneralInfoTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-2 overflow-y-auto p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài ngắn</Label>
          <div className='w-full'>
            <FormField type='text' label='' name='account_short_name' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tên ngắn khác</Label>
          <div className='w-full'>
            <FormField type='text' label='' name='account_other_short_name' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center gap-8'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tài khoản sổ cái</Label>
            <FormField type='checkbox' label='' name='is_ledger_account' disabled={formMode === 'view'} />
          </div>

          <div className='flex items-center'>
            <Label className='w-24 min-w-24'>Ngoại tệ gốc</Label>
            <div className='w-[100px]'>
              <FormField
                type='select'
                label=''
                name='foreign_currency'
                disabled={formMode === 'view'}
                options={[
                  { label: 'VND', value: 'VND' },
                  { label: 'USD', value: 'USD' },
                  { label: 'EUR', value: 'EUR' },
                  { label: 'JPY', value: 'JPY' }
                ]}
                defaultValue={'VND'}
              />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tk theo dõi công nợ</Label>
          <div className='w-[250px]'>
            <FormField
              type='select'
              label=''
              name='debt_tracking_account'
              disabled={formMode === 'view'}
              options={[
                { label: '0. Không theo dõi công nợ', value: 0 },
                { label: '1. Công nợ phải thu', value: 1 },
                { label: '2. Công nợ phải trả', value: 2 },
                { label: '3. Công nợ khác', value: 3 }
              ]}
              defaultValue={0}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Phân loại tài khoản</Label>
          <div className='w-[250px]'>
            <FormField
              type='select'
              label=''
              name='account_classification'
              disabled={formMode === 'view'}
              options={[
                { label: 'KPL. Không phân loại', value: 0 },
                { label: 'TA. Thuế', value: 1 },
                { label: 'TI. Tiền', value: 2 },
                { label: 'CN. Công nợ', value: 3 },
                { label: 'NH. Ngân hàng', value: 4 },
                { label: 'VV. Vụ việc', value: 5 },
                { label: 'HD. Hợp đồng', value: 6 },
                { label: 'KU. Khế ước', value: 7 },
                { label: 'MR1. Mở rộng 1', value: 8 },
                { label: 'MR2. Mở rộng 2', value: 9 },
                { label: 'MR3. Mở rộng 3', value: 10 }
              ]}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Pp tính tggs nợ</Label>
          <div className='w-[250px]'>
            <FormField
              type='select'
              label=''
              name='debt_calculation_method'
              disabled={formMode === 'view'}
              options={[
                { label: '0. Không tính chênh lệch', value: 0 },
                { label: '1. Trung bình tháng', value: 1 },
                { label: '2. Đích danh', value: 2 },
                { label: '3. Trung bình di động', value: 3 }
              ]}
              defaultValue={0}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Pp tính tggs có</Label>
          <div className='w-[250px]'>
            <FormField
              type='select'
              label=''
              name='credit_calculation_method'
              disabled={formMode === 'view'}
              options={[
                { label: '0. Không tính chênh lệch', value: 0 },
                { label: '1. Trung bình tháng', value: 1 },
                { label: '2. Đích danh', value: 2 },
                { label: '3. Trung bình di động', value: 3 }
              ]}
              defaultValue={0}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ghi chú</Label>
          <div className='w-full'>
            <FormField type='text' label='' name='account_note' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Trạng thái</Label>
          <div className='w-[250px]'>
            <FormField
              type='select'
              label=''
              name='account_status'
              disabled={formMode === 'view'}
              options={[
                { label: '1. Còn sử dụng', value: 'active' },
                { label: '2. Không sử dụng', value: 'inactive' }
              ]}
              defaultValue={'active'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeneralInfoTab;
