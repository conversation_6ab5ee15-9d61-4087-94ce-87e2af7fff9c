import React, { useState } from 'react';
import { Button } from '@mui/material';
import { AddFormValues, addSchema, initialValues } from '../../schema';
import { AritoDialog } from '@/components/custom/arito/dialog';
import type { <PERSON><PERSON><PERSON>, AccountModel } from '@/types/schemas';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import BasicInfo from '../BasicInfo';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onAdd: (data: any) => void;
}

const FormDialog = ({ open, onClose, onAdd }: FormDialogProps) => {
  // useState for SearchField components
  const [congCu, setCongCu] = useState<any | null>(null);
  const [boPhan, setBoPhan] = useState<BoPhan | null>(null);
  const [tkCongCu, setTkCongCu] = useState<AccountModel | null>(null);
  const [tkPhanBo, setTkPhanBo] = useState<AccountModel | null>(null);
  const [tkChiPhi, setTkChiPhi] = useState<AccountModel | null>(null);

  const handleSubmit = (data: AddFormValues) => {
    // Combine form data with SearchField selections
    const updatedData = {
      ...data,
      cong_cu: congCu?.uuid,
      bo_phan: boPhan?.uuid,
      tk_cong_cu: tkCongCu?.uuid,
      tk_phan_bo: tkPhanBo?.uuid,
      tk_chi_phi: tkChiPhi?.uuid
    };
    onAdd(updatedData);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Mới'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={281} />}
      actions={
        <>
          <Button
            type='submit'
            variant='contained'
            className='bg-[rgba(15,118,110,0.9)] normal-case text-white hover:bg-[rgba(15,118,110,1)]'
          >
            <AritoIcon icon={884} marginX='4px' />
            Đồng ý
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} marginX='4px' />
            Huỷ
          </Button>
        </>
      }
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={addSchema}
        onSubmit={handleSubmit}
        initialData={initialValues}
        className='w-[800px]'
        headerFields={
          <BasicInfo
            congCu={congCu}
            boPhan={boPhan}
            tkCongCu={tkCongCu}
            tkPhanBo={tkPhanBo}
            tkChiPhi={tkChiPhi}
            setCongCu={setCongCu}
            setBoPhan={setBoPhan}
            setTkCongCu={setTkCongCu}
            setTkPhanBo={setTkPhanBo}
            setTkChiPhi={setTkChiPhi}
          />
        }
      />
    </AritoDialog>
  );
};

export default FormDialog;
