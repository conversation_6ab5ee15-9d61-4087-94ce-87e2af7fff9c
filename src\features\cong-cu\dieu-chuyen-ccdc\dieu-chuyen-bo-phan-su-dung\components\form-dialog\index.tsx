import { Button } from '@mui/material';
import React from 'react';
import { AddFormValues, addSchema, initialValues } from '../../schema';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { BasicInfoTab } from './BasicInfoTab';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onAdd: (data: any) => void;
}

const FormDialog = ({ open, onClose, onAdd }: FormDialogProps) => {
  const handleSubmit = (data: AddFormValues) => {
    onAdd(data);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Mới'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={281} />}
      actions={
        <>
          <Button
            type='submit'
            variant='contained'
            className='bg-[rgba(15,118,110,0.9)] normal-case text-white hover:bg-[rgba(15,118,110,1)]'
          >
            <AritoIcon icon={884} marginX='4px' />
            Đồng ý
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} marginX='4px' />
            Huỷ
          </Button>
        </>
      }
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={addSchema}
        onSubmit={handleSubmit}
        initialData={initialValues}
        className='w-[800px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />
          </div>
        }
      />
    </AritoDialog>
  );
};

export default FormDialog;
