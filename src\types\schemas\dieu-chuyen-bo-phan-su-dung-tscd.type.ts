export interface DieuChuyenBoPhanSuDungTSCDType {
  /**
   * UUID of the record
   */
  uuid: string

  /**
   * The entity this record belongs to
   */
  entity_model: string

  /**
   * The fixed asset
   */
  ma_ts?: string

  /**
   * The period
   */
  ky: number

  /**
   * The year
   */
  nam: number

  /**
   * The department
   */
  ma_bp?: string

  /**
   * The asset account
   */
  tk_ts?: string

  /**
   * The depreciation account
   */
  tk_kh?: string

  /**
   * The expense account
   */
  tk_cp?: string
}

export type DieuChuyenBoPhanSuDungTSCDCreateInput = Omit<
  DieuChuyenBoPhanSuDungTSCDType,
  'uuid'
>

export type DieuChuyenBoPhanSuDungTSCDUpdateInput = Partial<DieuChuyenBoPhanSuDungTSCDCreateInput>
