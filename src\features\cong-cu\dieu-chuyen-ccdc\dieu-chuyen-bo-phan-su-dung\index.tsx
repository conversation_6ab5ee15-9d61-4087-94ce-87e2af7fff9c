'use client';

import React from 'react';
import { DieuChuyenBoPhanSuDungCCDCInput } from '@/types/schemas/dieu-chuyen-bo-phan-su-dung-ccdc.type';
import { useDieuChuyenBoPhanSuDungCCDC } from './hooks/useDieuChuyenBoPhanSuDungCCDC';
import { DeleteDialog, LoadingOverlay } from '@/components/custom/arito';
import { AritoDataTables } from '@/components/arito/arito-data-tables';
import { CCDCTransfersColumns } from './cols-definition';
import { FormDialog, ActionBar } from './components';
import { useRows } from '@/hooks/use-rows';
import { useFormState } from './hooks';

export default function DieuChuyenBoPhanSuDung({ initialRows }: { initialRows: any[] }) {
  const {
    dieuChuyenBoPhanSuDungCCDCs,
    isLoading,
    addDieuChuyenBoPhanSuDungCCDC,
    updateDieuChuyenBoPhanSuDungCCDC,
    deleteDieuChuyenBoPhanSuDungCCDC,
    refreshDieuChuyenBoPhanSuDungCCDCs
  } = useDieuChuyenBoPhanSuDungCCDC();

  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,
    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();

  const handleFormSubmit = async (data: DieuChuyenBoPhanSuDungCCDCInput) => {
    try {
      if (formMode === 'add') {
        await addDieuChuyenBoPhanSuDungCCDC(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updateDieuChuyenBoPhanSuDungCCDC({
          uuid: selectedObj.uuid,
          ...data
        });
      }
      handleCloseForm();
      clearSelection();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const handleDeleteConfirm = async () => {
    if (selectedObj) {
      try {
        await deleteDieuChuyenBoPhanSuDungCCDC(selectedObj.uuid);
        handleCloseDelete();
        clearSelection();
      } catch (error) {
        console.error('Error deleting tool transfer:', error);
      }
    }
  };

  const handleRefreshClick = () => {
    refreshDieuChuyenBoPhanSuDungCCDCs();
  };

  const handleFixedColumnsClick = () => {
    console.log('Fixed columns clicked');
  };

  const handleExportClick = () => {
    console.log('Export clicked');
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: dieuChuyenBoPhanSuDungCCDCs,
      columns: CCDCTransfersColumns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {isLoading && <LoadingOverlay />}

      {showForm && <FormDialog open={showForm} onClose={handleCloseForm} onAdd={handleFormSubmit} />}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          onConfirm={handleDeleteConfirm}
          title='Xác nhận xóa'
          message='Bạn có chắc chắn muốn xóa mục này không?'
        />
      )}

      <ActionBar
        onAddClick={handleAddClick}
        onEditClick={handleEditClick}
        onDeleteClick={handleDeleteClick}
        onCopyClick={handleCopyClick}
        onFixedColumnsClick={handleFixedColumnsClick}
        onRefreshClick={handleRefreshClick}
        onExportClick={handleExportClick}
      />

      <div className='w-full overflow-hidden'>
        <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
      </div>
    </div>
  );
}
