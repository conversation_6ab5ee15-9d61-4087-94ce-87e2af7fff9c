'use client';

import React from 'react';
import { AritoDataTables } from '@/components/arito/arito-data-tables';
import { CCDCTransfersColumns } from './cols-definition';
import { FormDialog, ActionBar } from './components';
import { useFormState, useData } from './hooks';

export default function DieuChuyenBoPhanSuDung({ initialRows }: { initialRows: any[] }) {
  const {
    showForm,
    showDelete,
    showCopy,
    showSearch,
    formMode,
    handleCloseForm,
    handleAddClick,
    handleEditClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const {
    rows,
    selectedRowIndex,
    handleRowClick,
    handleFormSubmit,
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportClick
  } = useData(initialRows);

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: CCDCTransfersColumns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {showForm && <FormDialog open={showForm} onClose={handleCloseForm} onAdd={handleFormSubmit} />}

      <ActionBar
        onAddClick={handleAddClick}
        onEditClick={handleEditClick}
        onDeleteClick={handleDeleteClick}
        onCopyClick={handleCopyClick}
        onFixedColumnsClick={handleFixedColumnsClick}
        onRefreshClick={handleRefreshClick}
        onExportClick={handleExportClick}
      />

      <div className='w-full overflow-hidden'>
        <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
      </div>
    </div>
  );
}
