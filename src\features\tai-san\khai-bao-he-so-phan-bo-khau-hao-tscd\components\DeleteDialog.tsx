import { Button } from '@mui/material';
import { useState } from 'react';
import { AritoDialog, AritoIcon } from '@/components/custom/arito';

interface DeleteDialogProps {
  open: boolean;
  onClose: () => void;
  selectedObj: any;
  deleteDoiTuong: (uuid: string) => Promise<any>;
  clearSelection: () => void;
}

const DeleteDialog = ({ open, onClose, selectedObj, deleteDoiTuong, clearSelection }: DeleteDialogProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDelete = async () => {
    if (!selectedObj) return;

    setIsDeleting(true);
    setError(null);

    try {
      await deleteDoiTuong(selectedObj.uuid);
      onClose();
      clearSelection();
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra khi xóa');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AritoDialog open={open} onClose={onClose} title='Xóa dữ liệu' maxWidth='xl' titleIcon={<AritoIcon icon={260} />}>
      <div className='flex w-[40vw] flex-col justify-between p-4'>
        <p className='mb-4'>Bạn có chắc chắn muốn xóa?</p>

        <div>
          {error && <div className='mb-4 rounded bg-red-100 p-2 text-red-700'>{error}</div>}

          <div className='flex justify-end space-x-2'>
            <Button
              className='bg-main hover:bg-main/80'
              onClick={handleDelete}
              variant='contained'
              disabled={isDeleting}
            >
              <AritoIcon icon={884} marginX='4px' />
              {isDeleting ? 'Đang xóa...' : 'Đồng ý'}
            </Button>
            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Hủy
            </Button>
          </div>
        </div>
      </div>
    </AritoDialog>
  );
};

export default DeleteDialog;
