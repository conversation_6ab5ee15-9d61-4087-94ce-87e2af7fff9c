'use client';

import React from 'react';
import { ActionBar, FormDialog, ConfirmDialog, InitialSearchDialog } from './components';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { useFormState, useData, useDialogState } from './hooks';
import { Columns } from './cols-definition';

export default function DeclareAllocationFactorClientPage({ initialRows }: { initialRows: any[] }) {
  const {
    showForm,
    showDelete,
    formMode,
    handleCloseForm,
    handleCloseDelete,
    handleCloseCopy,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const {
    initialSearchDialogOpen,
    showTable,
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    searchParams
  } = useDialogState();

  const {
    rows,
    selectedRowIndex,
    handleRowClick,
    handleFormSubmit,
    handleCopySubmit,
    handleDeleteConfirm,
    handleRefreshClick,
    handleFixedColumnsClick
  } = useData(initialRows);

  const tables = [
    {
      name: '',
      rows: rows,
      columns: Columns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />
      {showTable && (
        <>
          <ActionBar
            onAddClick={handleAddClick}
            onSearchClick={handleSearchClick}
            onEditClick={handleEditClick}
            onDeleteClick={handleDeleteClick}
            onCopyClick={handleCopyClick}
            onViewClick={handleViewClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onRefreshClick={handleRefreshClick}
          />

          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          </div>
        </>
      )}
      {showDelete && (
        <ConfirmDialog
          open={showDelete}
          onClose={handleCloseDelete}
          onConfirm={handleDeleteConfirm}
          title='Xoá dữ liệu'
          content='Bạn có chắc chắn muốn xoá không?'
        />
      )}
      {showForm && (
        <FormDialog
          open={showForm}
          onAdd={handleAddClick}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          formMode={formMode}
        />
      )}
    </div>
  );
}
