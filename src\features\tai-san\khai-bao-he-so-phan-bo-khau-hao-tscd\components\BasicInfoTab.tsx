'use client';

import {
  assetsColumns,
  BoPhanColDef,
  ContractColDef,
  FeeColDef,
  getAccountColumns,
  vatTuSearchColumns,
  VuViecColDef
} from '../cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const BasicInfoTab: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        {/* Tài sản */}
        <div className='flex items-center'>
          <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tài sản</Label>
          <SearchField
            type='text'
            displayRelatedField='ten_tai_san'
            columnDisplay='ma_tai_san'
            className='w-[11.25rem]'
            searchEndpoint='/api/tai-san'
            searchColumns={assetsColumns}
            dialogTitle='Danh mục tài sản'
          />
        </div>

        {/* Tài khoản khấu hao */}
        <div className='flex items-center'>
          <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tài khoản khấu hao</Label>
          <SearchField
            type='text'
            displayRelatedField='ten_tai_khoan'
            columnDisplay='so_tai_khoan'
            className='w-[11.25rem]'
            searchEndpoint='/api/tai-khoan'
            searchColumns={getAccountColumns}
            dialogTitle='Danh mục tài khoản'
          />
        </div>

        {/* Tài khoản chi phí */}
        <div className='flex items-center'>
          <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Tài khoản chi phí</Label>
          <SearchField
            type='text'
            displayRelatedField='ten_tai_khoan'
            columnDisplay='so_tai_khoan'
            className='w-[11.25rem]'
            searchEndpoint='/api/tai-khoan'
            searchColumns={getAccountColumns}
            dialogTitle='Danh mục tài khoản'
          />
        </div>

        {/* Bộ phận tài sản */}
        <div className='flex items-center'>
          <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Bộ phận tài sản</Label>
          <SearchField
            type='text'
            displayRelatedField='ten_bo_phan'
            columnDisplay='ma_bo_phan'
            className='w-[11.25rem]'
            searchEndpoint='/api/bo-phan'
            searchColumns={BoPhanColDef}
            dialogTitle='Danh mục bộ phận'
          />
        </div>

        {/* Bộ phận */}
        <div className='flex items-center'>
          <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Bộ phận</Label>
          <SearchField
            type='text'
            displayRelatedField='ten_bo_phan'
            columnDisplay='ma_bo_phan'
            className='w-[11.25rem]'
            searchEndpoint='/api/bo-phan'
            searchColumns={BoPhanColDef}
            dialogTitle='Danh mục bộ phận'
          />
        </div>

        {/* Vụ việc */}
        <div className='flex items-center'>
          <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Vụ việc</Label>
          <SearchField
            type='text'
            displayRelatedField='ten_vu_viec'
            columnDisplay='ma_vu_viec'
            className='w-[11.25rem]'
            searchEndpoint='/api/vu-viec'
            searchColumns={VuViecColDef}
            dialogTitle='Danh mục vụ việc'
          />
        </div>

        {/* Mã sản phẩm */}
        <div className='flex items-center'>
          <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã sản phẩm</Label>
          <SearchField
            type='text'
            displayRelatedField='ten_san_pham'
            columnDisplay='ma_san_pham'
            className='w-[11.25rem]'
            searchEndpoint='/api/san-pham'
            searchColumns={vatTuSearchColumns}
            dialogTitle='Danh mục sản phẩm'
          />
        </div>

        {/* Mã phí */}
        <div className='flex items-center'>
          <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã phí</Label>
          <SearchField
            type='text'
            displayRelatedField='ten_phi'
            columnDisplay='ma_phi'
            className='w-[11.25rem]'
            searchEndpoint='/api/phi'
            searchColumns={FeeColDef}
            dialogTitle='Danh mục phí'
          />
        </div>

        {/* Hợp đồng */}
        <div className='flex items-center'>
          <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Hợp đồng</Label>
          <SearchField
            type='text'
            displayRelatedField='ten_hop_dong'
            columnDisplay='ma_hop_dong'
            className='w-[11.25rem]'
            searchEndpoint='/api/hop-dong'
            searchColumns={ContractColDef}
            dialogTitle='Danh mục hợp đồng'
          />
        </div>

        {/* Hệ số */}
        <div className='flex items-center'>
          <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Hệ số</Label>
          <FormField type='number' name='he_so' className='w-[11.25rem]' defaultValue='0.000' />
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
