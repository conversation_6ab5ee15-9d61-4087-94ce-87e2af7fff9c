import { useState } from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { useDonViCoSo } from '@/hooks';

function BasicForm() {
  const { donViCoSos, isLoading } = useDonViCoSo();

  // options
  const donViOptions = donViCoSos.map(donVi => ({
    value: donVi.uuid,
    label: `${donVi.ma_unit} - ${donVi.ten_unit}`
  }));

  return (
    <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
      <div className='p-6'>
        <div className='flex flex-col gap-y-3'>
          {/* Kỳ */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='min-w-40'>Kỳ/Năm</Label>
            <div className='flex items-center gap-2'>
              <FormField name='ky' type='number' className='w-16' />
              <FormField name='nam' type='number' className='w-20' />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default BasicForm;
