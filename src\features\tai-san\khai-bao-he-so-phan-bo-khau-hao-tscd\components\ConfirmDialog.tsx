import { Button } from '@mui/material';
import React from 'react';
import { AritoDialog } from '@/components/custom/arito/dialog';
import AritoIcon from '@/components/custom/arito/icon';

interface ConfirmDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  content?: string;
}

const ConfirmDialog = ({
  open,
  onClose,
  onConfirm,
  title = 'Xác nhận',
  content = 'Bạn có chắc chắn muốn...?'
}: ConfirmDialogProps) => {
  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      maxWidth='sm'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      showFullscreenToggle={false}
      titleIcon={<AritoIcon icon={260} />}
      actions={
        <>
          <Button
            onClick={onConfirm}
            type='submit'
            variant='contained'
            className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
          >
            <AritoIcon icon={884} className='mr-2' />
            Đồng ý
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} className='mr-2' />
            Huỷ
          </Button>
        </>
      }
    >
      <p className='min-w-[40vw] p-4 text-base font-medium'>{content}</p>
    </AritoDialog>
  );
};

export default ConfirmDialog;
