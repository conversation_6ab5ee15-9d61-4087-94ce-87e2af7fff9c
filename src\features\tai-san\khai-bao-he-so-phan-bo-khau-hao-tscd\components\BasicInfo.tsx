import {
  accountSearchColumns,
  boPhanSearchColumns,
  congDoanSearchColumns,
  hopDongSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  taiSanSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  yeuToSearchColumns
} from '@/constants/search-columns';
import type { BoPhan, DanhMucCongDoan, HopDong, Phi, TaiKhoan, TaiSanCoDinh, VatTu, VuViec } from '@/types/schemas';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

interface BasicInfoProps {
  formMode: 'add' | 'edit' | 'view';
  taiSan?: any | null;
  setTaiSan?: (ts: any) => void;
  tkKhauHao?: TaiKhoan | null;
  setTkKhauHao?: (tk: TaiKhoan) => void;
  tkChiPhi?: TaiKhoan | null;
  setTkChiPhi?: (tk: TaiKhoan) => void;
  boPhanTS?: BoPhan | null;
  setBoPhanTS?: (boPhan: BoPhan) => void;
  boPhan?: BoPhan | null;
  setBoPhan?: (boPhan: BoPhan) => void;
  vuViec?: any | null;
  setVuViec?: (vuViec: any) => void;
  sanPham?: VatTu | null;
  setSanPham?: (vatTu: VatTu) => void;
  phi?: any | null;
  setPhi?: (phi: any) => void;
  hopDong?: any | null;
  setHopDong?: (hopDong: any) => void;
}

const BasicInfo = ({
  formMode,
  taiSan,
  setTaiSan,
  boPhan,
  setBoPhan,
  boPhanTS,
  setBoPhanTS,
  sanPham,
  setSanPham,
  vuViec,
  setVuViec,
  tkKhauHao,
  setTkKhauHao,
  tkChiPhi,
  setTkChiPhi,
  phi,
  setPhi,
  hopDong,
  setHopDong
}: BasicInfoProps) => {
  const isViewMode = formMode === 'view';

  return (
    <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
      <div className='flex flex-col gap-y-3 p-6'>
        {/* Mã tài sản */}
        <div className='flex items-center'>
          <Label className='mt-2 min-w-40'>Mã tài sản</Label>
          <SearchField<TaiSanCoDinh>
            dialogTitle='Danh mục tài sản cố định'
            searchEndpoint={`/${QUERY_KEYS.KHAI_BAO_THONG_TIN_TSCD}`}
            searchColumns={taiSanSearchColumns}
            displayRelatedField={'ten_ts'}
            columnDisplay={'ma_ts'}
            value={taiSan?.ma_ts || ''}
            relatedFieldValue={taiSan?.ten_ts || ''}
            onRowSelection={setTaiSan}
            disabled={isViewMode}
          />
        </div>

        {/* Tài khoản khấu hao */}
        <div className='flex items-center'>
          <Label className='mt-2 min-w-40'>Tài khoản khấu hao</Label>
          <SearchField<TaiKhoan>
            dialogTitle='Danh mục tài khoản'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountSearchColumns}
            displayRelatedField='name'
            columnDisplay='code'
            value={tkKhauHao?.code || ''}
            relatedFieldValue={tkKhauHao?.name || ''}
            onRowSelection={setTkKhauHao}
            disabled={isViewMode}
          />
        </div>

        {/* Tài khoản chi phí */}
        <div className='flex items-center'>
          <Label className='mt-2 min-w-40'>Tài khoản chi phí</Label>
          <SearchField<TaiKhoan>
            dialogTitle='Danh mục tài khoản'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountSearchColumns}
            displayRelatedField='name'
            columnDisplay='code'
            value={tkChiPhi?.code || ''}
            relatedFieldValue={tkChiPhi?.name || ''}
            onRowSelection={setTkChiPhi}
            disabled={isViewMode}
          />
        </div>

        {/* Bộ phận tài sản */}
        <div className='flex items-center'>
          <Label className='mt-2 min-w-40'>Bộ phận tài sản</Label>
          <SearchField<any>
            dialogTitle='Danh mục bộ phận sử dụng TS'
            searchEndpoint={`/${QUERY_KEYS.BO_PHAN_SU_DUNG_TSCD}`}
            searchColumns={boPhanSearchColumns}
            displayRelatedField='ten_bp'
            columnDisplay='ma_bp'
            value={boPhanTS?.ma_bp || ''}
            relatedFieldValue={boPhanTS?.ten_bp || ''}
            onRowSelection={setBoPhanTS}
            disabled={isViewMode}
          />
        </div>

        {/* Bộ phận */}
        <div className='flex items-center'>
          <Label className='mt-2 min-w-40'>Bộ phận</Label>
          <SearchField<BoPhan>
            dialogTitle='Danh mục bộ phận sử dụng TS'
            searchEndpoint={`/${QUERY_KEYS.BO_PHAN}`}
            searchColumns={boPhanSearchColumns}
            displayRelatedField='ten_bp'
            columnDisplay='ma_bp'
            value={boPhan?.ma_bp || ''}
            relatedFieldValue={boPhan?.ten_bp || ''}
            onRowSelection={setBoPhan}
            disabled={isViewMode}
          />
        </div>

        {/* Vụ việc */}
        <div className='flex items-center'>
          <Label className='mt-2 min-w-40'>Vụ việc</Label>
          <SearchField<VuViec>
            dialogTitle='Danh mục vụ việc'
            searchEndpoint={`/${QUERY_KEYS.VU_VIEC}`}
            searchColumns={vuViecSearchColumns}
            displayRelatedField='ma_vu_viec'
            columnDisplay='ten_vu_viec'
            value={vuViec?.ma_vu_viec || ''}
            relatedFieldValue={vuViec?.ten_vu_viec || ''}
            onRowSelection={setVuViec}
            disabled={isViewMode}
          />
        </div>
        {/* Mã sản phẩm */}
        <div className='flex items-center'>
          <Label className='mt-2 min-w-40'>Mã sản phẩm</Label>
          <SearchField<VatTu>
            dialogTitle='Danh mục vật tư'
            searchEndpoint={`/${QUERY_KEYS.VAT_TU}`}
            searchColumns={vatTuSearchColumns}
            displayRelatedField='ten_vt'
            columnDisplay='ma_vt'
            value={sanPham?.ma_vt || ''}
            relatedFieldValue={sanPham?.ten_vt || ''}
            onRowSelection={setSanPham}
            disabled={isViewMode}
          />
        </div>

        {/* Mã phí */}
        <div className='flex items-center'>
          <Label className='mt-2 min-w-40'>Mã phí</Label>
          <SearchField<Phi>
            dialogTitle='Danh mục mã phí'
            searchEndpoint={`/${QUERY_KEYS.PHI}`}
            searchColumns={phiSearchColumns}
            displayRelatedField='ten_phi'
            columnDisplay='ma_phi'
            value={phi?.ma_phi || ''}
            relatedFieldValue={phi?.ten_phi || ''}
            onRowSelection={setPhi}
            disabled={isViewMode}
          />
        </div>

        {/* Hợp đồng */}
        <div className='flex items-center'>
          <Label className='mt-2 min-w-40'>Hợp đồng</Label>
          <SearchField<HopDong>
            dialogTitle='Danh mục hợp đồng'
            searchEndpoint={`/${QUERY_KEYS.HOP_DONG}`}
            searchColumns={hopDongSearchColumns}
            displayRelatedField='ten_hd'
            columnDisplay='ma_hd'
            value={hopDong?.ma_hd || ''}
            relatedFieldValue={hopDong?.ten_hd || ''}
            onRowSelection={setHopDong}
            disabled={isViewMode}
          />
        </div>

        {/* Hệ số */}
        <div className='flex items-center'>
          <Label className='mt-2 min-w-40'>Hệ số</Label>
          <FormField name='he_so' type='number' className='w-40' disabled={isViewMode} />
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
