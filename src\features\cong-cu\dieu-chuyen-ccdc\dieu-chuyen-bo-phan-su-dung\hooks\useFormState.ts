import { useState } from 'react';

type FormMode = 'add' | 'edit' | 'view';

const useFormState = () => {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [showDelete, setShowDelete] = useState<boolean>(false);
  const [showCopy, setShowCopy] = useState<boolean>(false);
  const [showSearch, setShowSearch] = useState<boolean>(true);
  const [formMode, setFormMode] = useState<FormMode>('add');

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleAddClick = () => {
    setShowForm(true);
    setFormMode('add');
  };

  const handleEditClick = () => {
    // setShowForm(true);
    // setFormMode('edit');
  };

  const handleDeleteClick = () => {
    // setShowDelete(true);
    // setShowForm(false);
  };

  const handleCopyClick = () => {
    // setShowCopy(true);
    // setShowForm(false);
  };

  return {
    showForm,
    showDelete,
    showCopy,
    showSearch,
    formMode,
    handleCloseForm,
    handleAddClick,
    handleEditClick,
    handleDeleteClick,
    handleCopyClick
  };
};

export default useFormState;
