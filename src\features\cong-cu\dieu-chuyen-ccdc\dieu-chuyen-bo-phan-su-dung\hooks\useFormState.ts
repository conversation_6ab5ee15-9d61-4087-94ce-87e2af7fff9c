import { useState } from 'react';
import { FormMode } from '@/types/form';

export default function useFormState() {
  const [showForm, setShowForm] = useState(false);
  const [showDelete, setShowDelete] = useState(false);
  const [formMode, setFormMode] = useState<FormMode>('add');
  const [isCopyMode, setIsCopyMode] = useState(false);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleCloseDelete = () => {
    setShowDelete(false);
  };

  const handleAddClick = () => {
    setFormMode('add');
    setIsCopyMode(false);
    setShowForm(true);
  };

  const handleEditClick = () => {
    setFormMode('edit');
    setShowForm(true);
  };

  const handleViewClick = () => {
    setFormMode('view');
    setShowForm(true);
  };

  const handleDeleteClick = () => {
    setShowDelete(true);
  };

  const handleCopyClick = () => {
    setFormMode('add');
    setIsCopyMode(true);
    setShowForm(true);
  };

  return {
    showForm,
    showDelete,
    formMode,
    isCopyMode,
    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  };
}
