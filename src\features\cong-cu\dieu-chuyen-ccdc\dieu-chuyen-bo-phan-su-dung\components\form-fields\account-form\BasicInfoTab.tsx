import { AccountBasicInfoTab, AccountGeneralInfoTab, AccountContraintTab } from '../../form-fields';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns } from '../../../cols-definition';
import { SearchField } from '../../../components';
import { Label } from '@/components/ui/label';

interface BasicInfoTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản</Label>
          <FormField type='text' label='' name='account' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tên tài kho<PERSON>n</Label>
          <div className='w-full'>
            <FormField type='text' label='' name='account_name' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tên tiếng anh</Label>
          <div className='w-full'>
            <FormField type='text' label='' name='english_name' disabled={formMode === 'view'} />
          </div>
        </div>

        <SearchField
          name='parent_account'
          label='Tài khoản mẹ'
          formMode={formMode}
          searchColumns={accountSearchColumns}
          defaultSearchColumn='name'
          actionButtons={['add', 'edit']}
          headerFields={<AccountBasicInfoTab formMode={formMode} />}
          tabs={[
            {
              id: 'general_info_tab',
              label: 'Thông tin chung',
              component: <AccountGeneralInfoTab formMode={formMode} />
            },
            {
              id: 'contraint_tab',
              label: 'Ràng buộc',
              component: <AccountContraintTab formMode={formMode} />
            }
          ]}
        />

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tk đại diện khác</Label>
          <FormField type='text' label='' name='other_account' disabled={formMode === 'view'} />
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
