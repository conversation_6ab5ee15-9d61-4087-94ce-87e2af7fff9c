import { useState } from 'react';

const useData = (initialRows: any[]) => {
  const [rows, setRows] = useState<any[]>(
    initialRows.map(row => ({
      ...row,
      id: row.name // Use the name field as the id
    }))
  );
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const [currentObject, setCurrentObject] = useState<any>(null);

  const handleRowClick = (params: any) => {
    setSelectedRowIndex(params.row.id.toString());
  };

  const handleFormSubmit = (data: any) => {
    console.log('Form submitted with data:', data);
    setCurrentObject(data);
  };

  const handleRefreshClick = () => {
    console.log('Refresh clicked');
    // TODO: Implement refresh logic
  };

  const handleFixedColumnsClick = () => {
    console.log('Fixed columns clicked');
    // TODO: Implement fixed columns logic
  };

  const handleExportClick = () => {
    console.log('Export clicked');
    // TODO: Implement export logic
  };

  return {
    rows,
    setRows,
    selectedRowIndex,
    currentObject,
    handleRowClick,
    handleFormSubmit,
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportClick
  };
};

export default useData;
