import React from 'react';
import { ConstraintColumns } from '../../../cols-definition';
import { AritoInputTable } from '@/components/custom/arito';

interface ContraintTabProps {
  formMode: 'add' | 'edit' | 'view';
}

function ContraintTab({ formMode }: ContraintTabProps) {
  return <AritoInputTable columns={ConstraintColumns} tableActionButtons={['pin']} mode={formMode} />;
}

export default ContraintTab;
