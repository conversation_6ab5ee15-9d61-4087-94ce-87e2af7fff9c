import { <PERSON><PERSON><PERSON>, Plus, Trash, Copy, FileSearch, Refresh<PERSON><PERSON>, Pin, TextSearch } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onAddClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  onViewClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
  onSearchClick?: () => void;
  onCopyToNextPeriodClick?: () => void;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onViewClick,
  onFixedColumnsClick,
  onRefreshClick,
  onExportClick,
  onSearchClick,
  onCopyToNextPeriodClick
}) => (
  <AritoActionBar
    titleComponent={
      <div>
        <h1 className='text-xl font-bold'>Khai báo hệ số phân bổ khấu hao TSCĐ</h1>
        <div className='text-[10px] text-gray-500'>
          <p>
            <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
            <span className='font-semibold'>Kỳ 5, năm 2025</span>
          </p>
        </div>
      </div>
    }
  >
    {onAddClick && <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} />}
    {onEditClick && <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isViewDisabled} />}
    {onDeleteClick && <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} disabled={isViewDisabled} />}
    {onCopyClick && <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick} disabled={isViewDisabled} />}
    {onViewClick && <AritoActionButton title='Xem' icon={FileSearch} onClick={onViewClick} disabled={isViewDisabled} />}

    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Tìm kiếm',
          icon: <AritoIcon icon={12} />,
          onClick: onSearchClick,
          group: 0
        },
        {
          title: 'Sao chép sang kỳ sau',
          icon: <AritoIcon icon={770} />,
          onClick: onCopyToNextPeriodClick,
          group: 1
        },
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: onRefreshClick,
          group: 2
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: onFixedColumnsClick,
          group: 1
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: onExportClick,
          group: 3
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
