import { <PERSON>, FileText, Pencil, Plus, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import type { SearchFormValues } from './SearchDialog/searchSchema';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onViewClick: () => void;
  onRefreshClick: () => void;
  onSearchClick: () => void;
  searchData?: SearchFormValues; 
}

const ActionBar = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onViewClick,
  onRefreshClick,
  onSearchClick,
  searchData
}: ActionBarProps) => {
  return (
    <AritoActionBar
      titleComponent={
        <div>
          <h1 className='my-1.5 text-xl font-bold'><PERSON><PERSON> báo hệ số phân bổ khấu hao TSCĐ</h1>
          {searchData && (
            <div className='text-[10px] text-gray-500'>
              <p>
                <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
                <span className='font-semibold'>
                  Kỳ {searchData?.ky} năm {searchData?.nam}
                </span>
              </p>
            </div>
          )}
        </div>
      }
    >
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} />
      <AritoActionButton title='Xoá' variant='destructive' icon={Trash} onClick={onDeleteClick} />
      <AritoActionButton title='Sao chép' icon={FileText} onClick={onCopyClick} />
      <AritoActionButton title='Xem' icon={Eye} onClick={onViewClick} />
      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Tìm kiếm',
            icon: <AritoIcon icon={12} />,
            onClick: onSearchClick,
            group: 0
          },
          {
            title: 'Sao chép sang kỳ sau',
            icon: <AritoIcon icon={770} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefreshClick,
            group: 2
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: () => {},
            group: 2
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 3
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
