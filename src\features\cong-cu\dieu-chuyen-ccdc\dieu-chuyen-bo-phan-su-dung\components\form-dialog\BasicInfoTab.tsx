import { toolSearchColumns, sectionSearchColumns, accountSearchColumns } from '../../cols-definition';
import { AccountBasicInfoTab, AccountGeneralInfoTab, AccountContraintTab } from '../form-fields';
import { FormField } from '@/components/custom/arito/form/form-field';
import { SearchField } from '../../components';
import { Label } from '@/components/ui/label';

interface BasicInfoTabProps {
  formMode: 'view' | 'add' | 'edit';
}

export const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <SearchField
          name='tool'
          label='Công cụ'
          formMode={formMode}
          searchColumns={toolSearchColumns}
          defaultSearchColumn='name'
        />

        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Kỳ/Năm</Label>
            <FormField className='mr-2 w-20 min-w-[150px]' type='number' label='' name='period' />
            <FormField className='w-20 min-w-[150px]' type='number' label='' name='year' />
          </div>
        </div>

        <SearchField
          name='section'
          label='Bộ phận'
          formMode={formMode}
          searchColumns={sectionSearchColumns}
          defaultSearchColumn='name'
        />

        <SearchField
          name='tool_account'
          label='Tk công cụ'
          formMode={formMode}
          searchColumns={accountSearchColumns}
          defaultSearchColumn='name'
          actionButtons={['add', 'edit']}
          headerFields={<AccountBasicInfoTab formMode={formMode} />}
          tabs={[
            {
              id: 'general_info_tab',
              label: 'Thông tin chung',
              component: <AccountGeneralInfoTab formMode={formMode} />
            },
            {
              id: 'contraint_tab',
              label: 'Ràng buộc',
              component: <AccountContraintTab formMode={formMode} />
            }
          ]}
        />

        <SearchField
          name='allocation_account'
          label='Tk phân bổ'
          formMode={formMode}
          searchColumns={accountSearchColumns}
          defaultSearchColumn='name'
          actionButtons={['add', 'edit']}
          headerFields={<AccountBasicInfoTab formMode={formMode} />}
          tabs={[
            {
              id: 'general_info_tab',
              label: 'Thông tin chung',
              component: <AccountGeneralInfoTab formMode={formMode} />
            },
            {
              id: 'contraint_tab',
              label: 'Ràng buộc',
              component: <AccountContraintTab formMode={formMode} />
            }
          ]}
        />

        <SearchField
          name='expense_account'
          label='Tk chi phí'
          formMode={formMode}
          searchColumns={accountSearchColumns}
          defaultSearchColumn='name'
          actionButtons={['add', 'edit']}
          headerFields={<AccountBasicInfoTab formMode={formMode} />}
          tabs={[
            {
              id: 'general_info_tab',
              label: 'Thông tin chung',
              component: <AccountGeneralInfoTab formMode={formMode} />
            },
            {
              id: 'contraint_tab',
              label: 'Ràng buộc',
              component: <AccountContraintTab formMode={formMode} />
            }
          ]}
        />
      </div>
    </div>
  );
};
