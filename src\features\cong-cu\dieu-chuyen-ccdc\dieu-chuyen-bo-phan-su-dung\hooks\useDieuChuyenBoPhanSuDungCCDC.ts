import { DieuChuyenBoPhanSuDungCCDC, DieuChuyenBoPhanSuDungCCDCInput } from '@/types/schemas/dieu-chuyen-bo-phan-su-dung-ccdc.type';
import { useDieuChuyenBoPhanSuDungCCDC as useDieuChuyenBoPhanSuDungCCDCQuery } from '@/hooks/queries';

export const useDieuChuyenBoPhanSuDungCCDC = () => {
  const {
    dieuChuyenBoPhanSuDungCCDCs: transfers,
    isLoading,
    addDieuChuyenBoPhanSuDungCCDC: addTransferQuery,
    updateDieuChuyenBoPhanSuDungCCDC: updateTransferQuery,
    deleteDieuChuyenBoPhanSuDungCCDC: deleteTransferQuery,
    refreshDieuChuyenBoPhanSuDungCCDCs
  } = useDieuChuyenBoPhanSuDungCCDCQuery();

  const dieuChuyenBoPhanSuDungCCDCs = transfers;

  const addDieuChuyenBoPhanSuDungCCDC = async (formData: DieuChuyenBoPhanSuDungCCDCInput): Promise<DieuChuyenBoPhanSuDungCCDC> => {
    try {
      const apiData: DieuChuyenBoPhanSuDungCCDCInput = {
        cong_cu: formData.cong_cu,
        ky: typeof formData.ky === 'string' ? (formData.ky === '' ? 0 : Number(formData.ky)) : formData.ky || 0,
        nam: typeof formData.nam === 'string' ? (formData.nam === '' ? 0 : Number(formData.nam)) : formData.nam || 0,
        bo_phan: formData.bo_phan,
        tk_cong_cu: formData.tk_cong_cu,
        tk_phan_bo: formData.tk_phan_bo,
        tk_chi_phi: formData.tk_chi_phi,
        status: formData.status
      };
      return await addTransferQuery(apiData);
    } catch (error) {
      console.error('Error adding tool transfer:', error);
      throw error;
    }
  };

  const updateDieuChuyenBoPhanSuDungCCDC = async (data: any & { uuid: string }): Promise<DieuChuyenBoPhanSuDungCCDC> => {
    try {
      const { uuid, ...formData } = data;

      const apiData: DieuChuyenBoPhanSuDungCCDCInput = {
        cong_cu: formData.cong_cu,
        ky: typeof formData.ky === 'string' ? (formData.ky === '' ? 0 : Number(formData.ky)) : formData.ky || 0,
        nam: typeof formData.nam === 'string' ? (formData.nam === '' ? 0 : Number(formData.nam)) : formData.nam || 0,
        bo_phan: formData.bo_phan,
        tk_cong_cu: formData.tk_cong_cu,
        tk_phan_bo: formData.tk_phan_bo,
        tk_chi_phi: formData.tk_chi_phi,
        status: formData.status
      };

      return await updateTransferQuery(uuid, apiData);
    } catch (error) {
      console.error('Error updating tool transfer:', error);
      throw error;
    }
  };

  const deleteDieuChuyenBoPhanSuDungCCDC = async (uuid: string): Promise<void> => {
    try {
      await deleteTransferQuery(uuid);
    } catch (error) {
      console.error('Error deleting tool transfer:', error);
      throw error;
    }
  };

  return {
    dieuChuyenBoPhanSuDungCCDCs,
    isLoading,
    addDieuChuyenBoPhanSuDungCCDC,
    updateDieuChuyenBoPhanSuDungCCDC,
    deleteDieuChuyenBoPhanSuDungCCDC,
    refreshDieuChuyenBoPhanSuDungCCDCs
  };
};
