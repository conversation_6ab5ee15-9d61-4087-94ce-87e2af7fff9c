import { useState, useEffect } from 'react';

export const useSearchFieldStates = (initialData?: any) => {
  const [taiSan, setTaiSan] = useState<any | null>(initialData?.ma_ts || null);
  const [tkKhauHao, setTkKhauHao] = useState<any | null>(initialData?.tk_kh_data || null);
  const [tkChiPhi, setTkChiPhi] = useState<any | null>(initialData?.tk_cp_data || null);
  const [boPhanTS, setBoPhanTS] = useState<any | null>(initialData?.ma_bp_ts_data || null);
  const [boPhan, setBoPhan] = useState<any | null>(initialData?.ma_bp_data || null);
  const [sanPham, setSanPham] = useState<any | null>(initialData?.ma_sp_data || null);
  const [vuViec, setVuViec] = useState<any | null>(initialData?.ma_vv_data || null);
  const [phi, setPhi] = useState<any | null>(initialData?.ma_phi_data || null);
  const [hopDong, setHopDong] = useState<any | null>(initialData?.ma_hd_data || null);

  // Update state when initialData changes
  
  return {
    taiSan,
    setTaiSan,
    boPhan,
    setBoPhan,
    boPhanTS,
    setBoPhanTS,
    sanPham,
    setSanPham,
    vuViec,
    setVuViec,
    tkKhauHao,
    setTkKhauHao,
    tkChiPhi,
    setTkChiPhi,
    phi,
    setPhi,
    hopDong,
    setHopDong
  };
};
