/**
 * TypeScript interface for DieuChuyenBoPhanSuDungCCDC (Tool Transfer Department Usage) model
 *
 * This interface represents the structure for transferring tools between departments.
 */

import { ApiResponse } from '@/types/api.type';

export interface DieuChuyenBoPhanSuDungCCDC {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model?: string;

  /**
   * Tool/Equipment UUID reference
   */
  cong_cu: string;

  /**
   * Tool/Equipment details (read-only)
   */
  cong_cu_data?: {
    ma_ccdc: string;
    ten_ccdc: string;
  };

  /**
   * Period (Kỳ)
   */
  ky: number;

  /**
   * Year (Năm)
   */
  nam: number;

  /**
   * Department UUID reference (Bộ phận)
   */
  bo_phan: string;

  /**
   * Department details (read-only)
   */
  bo_phan_data?: {
    ma_bp: string;
    ten_bp: string;
  };

  /**
   * Tool Account UUID reference (Tk công cụ)
   */
  tk_cong_cu: string;

  /**
   * Tool Account details (read-only)
   */
  tk_cong_cu_data?: {
    ma_tk: string;
    ten_tk: string;
  };

  /**
   * Allocation Account UUID reference (Tk phân bổ)
   */
  tk_phan_bo: string;

  /**
   * Allocation Account details (read-only)
   */
  tk_phan_bo_data?: {
    ma_tk: string;
    ten_tk: string;
  };

  /**
   * Cost Account UUID reference (Tk chi phí)
   */
  tk_chi_phi: string;

  /**
   * Cost Account details (read-only)
   */
  tk_chi_phi_data?: {
    ma_tk: string;
    ten_tk: string;
  };

  /**
   * Status indicator (0=inactive, 1=active)
   */
  status: string;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Type for DieuChuyenBoPhanSuDungCCDC API response
 */
export type DieuChuyenBoPhanSuDungCCDCResponse = ApiResponse<DieuChuyenBoPhanSuDungCCDC>;

/**
 * Type for creating or updating a DieuChuyenBoPhanSuDungCCDC
 */
export interface DieuChuyenBoPhanSuDungCCDCInput {
  /**
   * Tool/Equipment UUID reference
   */
  cong_cu: string;

  /**
   * Period (Kỳ)
   */
  ky: number;

  /**
   * Year (Năm)
   */
  nam: number;

  /**
   * Department UUID reference (Bộ phận)
   */
  bo_phan: string;

  /**
   * Tool Account UUID reference (Tk công cụ)
   */
  tk_cong_cu: string;

  /**
   * Allocation Account UUID reference (Tk phân bổ)
   */
  tk_phan_bo: string;

  /**
   * Cost Account UUID reference (Tk chi phí)
   */
  tk_chi_phi: string;

  /**
   * Status indicator (0=inactive, 1=active)
   */
  status: string;
}

/**
 * Type for form values with additional UI state
 */
export interface DieuChuyenBoPhanSuDungCCDCFormValues extends DieuChuyenBoPhanSuDungCCDCInput {
  // Additional form-specific fields can be added here if needed
}
