import { ApiResponse } from '@/types/api.type';

export interface HeSoPhanBoTSCD {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model?: string;

  /**
   * Depreciation period (1-12)
   */
  ky: number;

  /**
   * Depreciation year (1900-9999)
   */
  nam: number;

  /**
   * Asset code
   */
  ma_ts: string;

  /**
   * Depreciation coefficient
   */
  he_so: number;

  /**
   * Depreciation account ID
   */
  tk_kh: string;

  /**
   * Expense account ID
   */
  tk_cp: string;

  /**
   * Asset management department ID (optional)
   */
  ma_bp_ts?: string;

  /**
   * Usage department ID (optional)
   */
  ma_bp?: string;

  /**
   * Task/job code ID (optional)
   */
  ma_vv?: string;

  /**
   * Product code ID (optional)
   */
  ma_sp?: string;

  /**
   * Fee code ID (optional)
   */
  ma_phi?: string;

  /**
   * Contract code ID (optional)
   */
  ma_hd?: string;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Type for HeSoPhanBoTSCD API response
 */
export type HeSoPhanBoTSCDResponse = ApiResponse<HeSoPhanBoTSCD>;

/**
 * Type for creating or updating a HeSoPhanBoTSCD
 */
export interface HeSoPhanBoTSCDInput {
  uuid?: string;

  /**
   * Depreciation period (1-12)
   */
  ky: number;

  /**
   * Depreciation year (1900-9999)
   */
  nam: number;

  /**
   * Asset code
   */
  ma_ts?: string;

  /**
   * Depreciation coefficient
   */
  he_so: number;

  /**
   * Depreciation account ID
   */
  tk_kh?: string;

  /**
   * Expense account ID
   */
  tk_cp?: string;

  /**
   * Asset management department ID (optional)
   */
  ma_bp_ts?: string;

  /**
   * Usage department ID (optional)
   */
  ma_bp?: string;

  /**
   * Task/job code ID (optional)
   */
  ma_vv?: string;

  /**
   * Product code ID (optional)
   */
  ma_sp?: string;

  /**
   * Fee code ID (optional)
   */
  ma_phi?: string;

  /**
   * Contract code ID (optional)
   */
  ma_hd?: string;
}
