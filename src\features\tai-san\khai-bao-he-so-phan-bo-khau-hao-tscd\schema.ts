import { z } from 'zod';

export const FormSchema = z.object({
  ky: z.number().int().optional(),

  nam: z.number().int().optional(),

  ma_ts: z.string().optional(),

  he_so: z.coerce.number().optional(),

  tk_kh: z.string().optional(),

  tk_cp: z.string().optional(),

  ma_bp_ts: z.string().optional(),

  ma_bp: z.string().optional(),

  ma_vv: z.string().optional(),

  ma_sp: z.string().optional(),

  ma_phi: z.string().optional(),

  ma_hd: z.string().optional()
});

export type HeSoPhanBoTSCDSchemaType = z.infer<typeof FormSchema>;

export const initialValues: HeSoPhanBoTSCDSchemaType = {
  ky: 0,
  nam: 0,
  ma_ts: '',
  he_so: 0,
  tk_kh: '',
  tk_cp: '',
  ma_bp_ts: '',
  ma_bp: '',
  ma_vv: '',
  ma_sp: '',
  ma_phi: '',
  ma_hd: ''
};
