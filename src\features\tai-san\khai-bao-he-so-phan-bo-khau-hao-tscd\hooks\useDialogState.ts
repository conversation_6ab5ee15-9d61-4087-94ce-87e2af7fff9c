import { useState } from 'react';

export interface SearchParams {
  [key: string]: any;
}

export interface UseDialogStateReturn {
  initialSearchDialogOpen: boolean;
  showTable: boolean;
  handleInitialSearchClose: () => void;
  handleInitialSearch: (values: SearchParams) => void;
  handleSearchClick: () => void;
  setShowTable: (show: boolean) => void;
  searchParams: SearchParams | null;
}

export function useDialogState(): UseDialogStateReturn {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [showTable, setShowTable] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchParams | null>(null);

  const handleInitialSearchClose = () => {
    setInitialSearchDialogOpen(false);
  };

  const handleInitialSearch = (values: SearchParams) => {
    setSearchParams(values);
    setShowTable(true);
    setInitialSearchDialogOpen(false);
  };

  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  return {
    initialSearchDialogOpen,
    showTable,
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    setShowTable,
    searchParams
  };
}
